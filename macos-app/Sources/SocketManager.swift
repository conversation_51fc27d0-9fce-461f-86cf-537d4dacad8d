import Foundation
import SocketIO
import Logging
import SwiftyJSON
import UserNotifications

class SocketManager {
    private let manager: SocketIO.SocketManager
    private let socket: SocketIOClient
    private let logger = Logger(label: "WOWMacOS.SocketManager")
    
    // Callbacks
    var onConnect: (() -> Void)?
    var onDisconnect: (() -> Void)?
    var onServiceUpdate: (([ServiceInfo]) -> Void)?
    var onDockerUpdate: (([DockerContainer]) -> Void)?
    var onMetricsUpdate: ((SystemMetrics) -> Void)?
    var onNotification: ((NotificationItem) -> Void)?
    var onLogUpdate: (([String]) -> Void)?
    
    init(serverURL: String = "http://localhost:3000") {
        guard let url = URL(string: serverURL) else {
            fatalError("Invalid server URL: \(serverURL)")
        }
        
        let config: SocketIOClientConfiguration = [
            .log(false),
            .compress,
            .reconnects(true),
            .reconnectAttempts(5),
            .reconnectWait(2),
            .forceWebsockets(true)
        ]
        
        self.manager = SocketIO.SocketManager(socketURL: url, config: config)
        self.socket = manager.defaultSocket
        
        setupEventHandlers()
    }
    
    private func setupEventHandlers() {
        // Connection events
        socket.on(clientEvent: .connect) { [weak self] data, ack in
            self?.logger.info("Connected to WOW backend")
            self?.onConnect?()
            self?.subscribeToEvents()
        }
        
        socket.on(clientEvent: .disconnect) { [weak self] data, ack in
            self?.logger.warning("Disconnected from WOW backend")
            self?.onDisconnect?()
        }
        
        socket.on(clientEvent: .error) { [weak self] data, ack in
            self?.logger.error("Socket error: \(data)")
        }
        
        socket.on(clientEvent: .reconnect) { [weak self] data, ack in
            self?.logger.info("Reconnected to WOW backend")
            self?.subscribeToEvents()
        }
        
        // Custom events
        socket.on("service:update") { [weak self] data, ack in
            self?.handleServiceUpdate(data: data)
        }
        
        socket.on("docker:update") { [weak self] data, ack in
            self?.handleDockerUpdate(data: data)
        }
        
        socket.on("metrics:update") { [weak self] data, ack in
            self?.handleMetricsUpdate(data: data)
        }
        
        socket.on("notification") { [weak self] data, ack in
            self?.handleNotification(data: data)
        }
        
        socket.on("logs:update") { [weak self] data, ack in
            self?.handleLogUpdate(data: data)
        }
        
        socket.on("system:alert") { [weak self] data, ack in
            self?.handleSystemAlert(data: data)
        }
    }
    
    func connect() {
        logger.info("Connecting to WOW backend...")
        socket.connect()
    }
    
    func disconnect() {
        logger.info("Disconnecting from WOW backend...")
        socket.disconnect()
    }
    
    private func subscribeToEvents() {
        // Subscribe to real-time updates
        socket.emit("subscribe", [
            "services",
            "docker",
            "metrics",
            "logs",
            "notifications"
        ])
        
        logger.info("Subscribed to real-time events")
    }
    
    // MARK: - Event Handlers
    
    private func handleServiceUpdate(data: [Any]) {
        guard let jsonData = data.first,
              let services = parseServices(from: jsonData) else {
            logger.warning("Failed to parse service update data")
            return
        }
        
        logger.debug("Received service update: \(services.count) services")
        onServiceUpdate?(services)
    }
    
    private func handleDockerUpdate(data: [Any]) {
        guard let jsonData = data.first,
              let containers = parseDockerContainers(from: jsonData) else {
            logger.warning("Failed to parse docker update data")
            return
        }
        
        logger.debug("Received docker update: \(containers.count) containers")
        onDockerUpdate?(containers)
    }
    
    private func handleMetricsUpdate(data: [Any]) {
        guard let jsonData = data.first,
              let metrics = parseSystemMetricsFromData(jsonData) else {
            logger.warning("Failed to parse metrics update data")
            return
        }
        
        logger.debug("Received metrics update")
        onMetricsUpdate?(metrics)
    }
    
    private func handleNotification(data: [Any]) {
        guard let jsonData = data.first else {
            logger.warning("Failed to parse notification data")
            return
        }
        
        let json = JSON(jsonData)
        let notification = NotificationItem(
            id: UUID(),
            message: json["message"].stringValue,
            type: parseNotificationType(json["type"].stringValue),
            timestamp: Date()
        )
        
        logger.debug("Received notification: \(notification.message)")
        onNotification?(notification)
    }
    
    private func handleLogUpdate(data: [Any]) {
        guard let jsonData = data.first else {
            logger.warning("Failed to parse log update data")
            return
        }
        
        let json = JSON(jsonData)
        let logs = json["logs"].arrayValue.map { $0.stringValue }
        
        logger.debug("Received log update: \(logs.count) entries")
        onLogUpdate?(logs)
    }
    
    private func handleSystemAlert(data: [Any]) {
        guard let jsonData = data.first else {
            logger.warning("Failed to parse system alert data")
            return
        }
        
        let json = JSON(jsonData)
        let alert = NotificationItem(
            id: UUID(),
            message: json["message"].stringValue,
            type: .warning,
            timestamp: Date()
        )
        
        logger.warning("System alert: \(alert.message)")
        onNotification?(alert)
        
        // Send native notification
        sendNativeNotification(alert)
    }
    
    // MARK: - Parsing Methods
    
    private func parseServices(from data: Any) -> [ServiceInfo]? {
        let json = JSON(data)
        return json.arrayValue.compactMap { serviceJson in
            parseServiceInfo(from: serviceJson)
        }
    }
    
    private func parseDockerContainers(from data: Any) -> [DockerContainer]? {
        let json = JSON(data)
        return json.arrayValue.compactMap { containerJson in
            parseDockerContainer(from: containerJson)
        }
    }
    
    private func parseSystemMetricsFromData(_ data: Any) -> SystemMetrics? {
        let json = JSON(data)
        return parseSystemMetrics(from: json)
    }
    
    private func parseServiceInfo(from json: JSON) -> ServiceInfo? {
        guard let id = json["id"].string,
              let name = json["name"].string else {
            return nil
        }
        
        let statusString = json["status"].stringValue
        let status = ServiceStatus(rawValue: statusString) ?? .unknown
        
        let port = json["port"].int
        let url = json["url"].string
        let description = json["description"].string
        
        let lastUpdatedString = json["lastUpdated"].stringValue
        let lastUpdated = ISO8601DateFormatter().date(from: lastUpdatedString) ?? Date()
        
        return ServiceInfo(
            id: id,
            name: name,
            status: status,
            port: port,
            url: url,
            description: description,
            lastUpdated: lastUpdated,
            healthCheck: nil,
            metrics: nil
        )
    }
    
    private func parseDockerContainer(from json: JSON) -> DockerContainer? {
        guard let id = json["id"].string,
              let name = json["name"].string,
              let image = json["image"].string else {
            return nil
        }
        
        let statusString = json["status"].stringValue
        let status = ContainerStatus(rawValue: statusString) ?? .exited
        
        let state = json["state"].stringValue
        let createdString = json["created"].stringValue
        let created = ISO8601DateFormatter().date(from: createdString) ?? Date()
        
        return DockerContainer(
            id: id,
            name: name,
            image: image,
            status: status,
            state: state,
            ports: [],
            created: created,
            started: nil,
            networks: [],
            volumes: [],
            environment: [:],
            labels: [:],
            stats: nil
        )
    }
    
    private func parseSystemMetrics(from json: JSON) -> SystemMetrics {
        let cpu = CPUMetrics(
            usage: json["cpu"]["usage"].doubleValue,
            cores: json["cpu"]["cores"].intValue,
            frequency: json["cpu"]["frequency"].double,
            temperature: json["cpu"]["temperature"].double
        )
        
        let memory = MemoryMetrics(
            total: json["memory"]["total"].int64Value,
            used: json["memory"]["used"].int64Value,
            free: json["memory"]["free"].int64Value,
            available: json["memory"]["available"].int64Value,
            cached: json["memory"]["cached"].int64Value,
            buffers: json["memory"]["buffers"].int64Value,
            swapTotal: json["memory"]["swapTotal"].int64Value,
            swapUsed: json["memory"]["swapUsed"].int64Value
        )
        
        let disk = DiskMetrics(
            total: json["disk"]["total"].int64Value,
            used: json["disk"]["used"].int64Value,
            free: json["disk"]["free"].int64Value,
            readBytes: json["disk"]["readBytes"].int64Value,
            writeBytes: json["disk"]["writeBytes"].int64Value,
            readOps: json["disk"]["readOps"].int64Value,
            writeOps: json["disk"]["writeOps"].int64Value
        )
        
        let network = NetworkMetrics(
            bytesReceived: json["network"]["bytesReceived"].int64Value,
            bytesSent: json["network"]["bytesSent"].int64Value,
            packetsReceived: json["network"]["packetsReceived"].int64Value,
            packetsSent: json["network"]["packetsSent"].int64Value,
            errorsReceived: json["network"]["errorsReceived"].int64Value,
            errorsSent: json["network"]["errorsSent"].int64Value,
            downloadBytesPerSec: json["network"]["downloadBytesPerSec"].int64Value,
            uploadBytesPerSec: json["network"]["uploadBytesPerSec"].int64Value,
            errors: json["network"]["errors"].int64Value,
            drops: json["network"]["drops"].int64Value
        )
        
        let uptime = json["uptime"].doubleValue
        let loadAverage = json["loadAverage"].arrayValue.map { $0.doubleValue }
        
        let timestampString = json["timestamp"].stringValue
        let timestamp = ISO8601DateFormatter().date(from: timestampString) ?? Date()
        
        return SystemMetrics(
            cpu: cpu,
            memory: memory,
            disk: disk,
            network: network,
            uptime: uptime,
            loadAverage: loadAverage,
            processes: json["processes"].intValue,
            runningProcesses: json["runningProcesses"].intValue,
            sleepingProcesses: json["sleepingProcesses"].intValue,
            zombieProcesses: json["zombieProcesses"].intValue,
            timestamp: timestamp,
            bootTime: ISO8601DateFormatter().date(from: json["bootTime"].stringValue) ?? Date()
        )
    }
    
    private func parseNotificationType(_ typeString: String) -> NotificationType {
        switch typeString.lowercased() {
        case "info": return .info
        case "success": return .success
        case "warning": return .warning
        case "error": return .error
        default: return .info
        }
    }
    
    // MARK: - Native Notifications
    
    private func sendNativeNotification(_ notification: NotificationItem) {
        let content = UNMutableNotificationContent()
        content.title = "WOW System Alert"
        content.body = notification.message
        content.sound = .default
        
        let request = UNNotificationRequest(
            identifier: notification.id.uuidString,
            content: content,
            trigger: nil
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                self.logger.error("Failed to send notification: \(error)")
            }
        }
    }
}

// MARK: - Extensions

extension SocketManager {
    var isConnected: Bool {
        return socket.status == .connected
    }
    
    func emit(_ event: String, _ items: [Any]) {
        socket.emit(event, items)
    }
    
    func emit(_ event: String, _ items: [Any], completion: @escaping () -> Void) {
        socket.emit(event, items, completion: completion)
    }
}