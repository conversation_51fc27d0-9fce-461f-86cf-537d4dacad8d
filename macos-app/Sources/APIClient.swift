import Foundation
import Alamofire
import SwiftyJSON
import Logging

class APIClient {
    private let baseURL: String
    private let session: Session
    private let logger = Logger(label: "WOWMacOS.APIClient")
    
    init(baseURL: String = "http://localhost:3000") {
        self.baseURL = baseURL
        
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        
        self.session = Session(configuration: configuration)
    }
    
    // MARK: - Server Status
    func getServerStatus() async throws -> ServerStatus {
        let response = try await makeRequest(
            endpoint: "/api/trpc/health.status",
            method: .get
        )
        
        let json = JSON(response)
        let status = json["result"]["data"]["status"].stringValue
        
        switch status {
        case "healthy": return .healthy
        case "warning": return .warning
        case "error": return .error
        default: return .unknown
        }
    }
    
    // MARK: - Services
    func getServices() async throws -> [ServiceInfo] {
        let response = try await makeRequest(
            endpoint: "/api/trpc/services.list",
            method: .get
        )
        
        let json = JSON(response)
        let servicesData = json["result"]["data"].arrayValue
        
        return servicesData.compactMap { serviceJson in
            parseServiceInfo(from: serviceJson)
        }
    }
    
    func restartService(_ serviceId: String) async throws {
        let parameters = ["serviceId": serviceId]
        
        _ = try await makeRequest(
            endpoint: "/api/trpc/services.restart",
            method: .post,
            parameters: parameters
        )
    }
    
    func stopService(_ serviceId: String) async throws {
        let parameters = ["serviceId": serviceId]
        
        _ = try await makeRequest(
            endpoint: "/api/trpc/services.stop",
            method: .post,
            parameters: parameters
        )
    }
    
    func startService(_ serviceId: String) async throws {
        let parameters = ["serviceId": serviceId]
        
        _ = try await makeRequest(
            endpoint: "/api/trpc/services.start",
            method: .post,
            parameters: parameters
        )
    }
    
    // MARK: - Docker Containers
    func getDockerContainers() async throws -> [DockerContainer] {
        let response = try await makeRequest(
            endpoint: "/api/trpc/docker.containers",
            method: .get
        )
        
        let json = JSON(response)
        let containersData = json["result"]["data"].arrayValue
        
        return containersData.compactMap { containerJson in
            parseDockerContainer(from: containerJson)
        }
    }
    
    func restartDockerContainer(_ containerId: String) async throws {
        let parameters = ["containerId": containerId]
        
        _ = try await makeRequest(
            endpoint: "/api/trpc/docker.restart",
            method: .post,
            parameters: parameters
        )
    }
    
    func stopDockerContainer(_ containerId: String) async throws {
        let parameters = ["containerId": containerId]
        
        _ = try await makeRequest(
            endpoint: "/api/trpc/docker.stop",
            method: .post,
            parameters: parameters
        )
    }
    
    func startDockerContainer(_ containerId: String) async throws {
        let parameters = ["containerId": containerId]
        
        _ = try await makeRequest(
            endpoint: "/api/trpc/docker.start",
            method: .post,
            parameters: parameters
        )
    }
    
    // MARK: - System Metrics
    func getSystemMetrics() async throws -> SystemMetrics {
        let response = try await makeRequest(
            endpoint: "/api/trpc/system.metrics",
            method: .get
        )
        
        let json = JSON(response)
        let metricsData = json["result"]["data"]
        
        return parseSystemMetrics(from: metricsData)
    }
    
    // MARK: - Logs
    func getLogs(service: String? = nil, limit: Int = 100) async throws -> [String] {
        var parameters: [String: Any] = ["limit": limit]
        if let service = service {
            parameters["service"] = service
        }
        
        let response = try await makeRequest(
            endpoint: "/api/trpc/logs.get",
            method: .get,
            parameters: parameters
        )
        
        let json = JSON(response)
        return json["result"]["data"].arrayValue.map { $0.stringValue }
    }
    
    // MARK: - Database
    func getDatabaseStatus() async throws -> [String: Any] {
        let response = try await makeRequest(
            endpoint: "/api/trpc/database.status",
            method: .get
        )
        
        let json = JSON(response)
        return json["result"]["data"].dictionaryObject ?? [:]
    }
    
    // MARK: - Private Methods
    private func makeRequest(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil
    ) async throws -> Any {
        let url = baseURL + endpoint
        
        return try await withCheckedThrowingContinuation { continuation in
            var request = session.request(
                url,
                method: method,
                parameters: parameters,
                encoding: method == .get ? URLEncoding.default : JSONEncoding.default,
                headers: headers
            )
            
            request.responseJSON { response in
                switch response.result {
                case .success(let value):
                    self.logger.debug("API request successful: \(endpoint)")
                    continuation.resume(returning: value)
                    
                case .failure(let error):
                    self.logger.error("API request failed: \(endpoint) - \(error)")
                    continuation.resume(throwing: APIError.networkError(error))
                }
            }
        }
    }
    
    private func parseServiceInfo(from json: JSON) -> ServiceInfo? {
        guard let id = json["id"].string,
              let name = json["name"].string else {
            return nil
        }
        
        let statusString = json["status"].stringValue
        let status = ServiceStatus(rawValue: statusString) ?? .unknown
        
        let port = json["port"].int
        let url = json["url"].string
        let description = json["description"].string
        
        let lastUpdatedString = json["lastUpdated"].stringValue
        let lastUpdated = ISO8601DateFormatter().date(from: lastUpdatedString) ?? Date()
        
        let healthCheck = parseHealthCheck(from: json["healthCheck"])
        let metrics = parseServiceMetrics(from: json["metrics"])
        
        return ServiceInfo(
            id: id,
            name: name,
            status: status,
            port: port,
            url: url,
            description: description,
            lastUpdated: lastUpdated,
            healthCheck: healthCheck,
            metrics: metrics
        )
    }
    
    private func parseHealthCheck(from json: JSON) -> HealthCheck? {
        guard json.exists() else { return nil }
        
        let endpoint = json["endpoint"].stringValue
        let interval = json["interval"].intValue
        let timeout = json["timeout"].intValue
        let retries = json["retries"].intValue
        
        let lastCheckString = json["lastCheck"].string
        let lastCheck = lastCheckString.flatMap { ISO8601DateFormatter().date(from: $0) }
        
        let statusString = json["status"].stringValue
        let status = HealthStatus(rawValue: statusString) ?? .unknown
        
        return HealthCheck(
            endpoint: endpoint,
            interval: interval,
            timeout: timeout,
            retries: retries,
            lastCheck: lastCheck,
            status: status
        )
    }
    
    private func parseServiceMetrics(from json: JSON) -> ServiceMetrics? {
        guard json.exists() else { return nil }
        
        return ServiceMetrics(
            cpuUsage: json["cpuUsage"].doubleValue,
            memoryUsage: json["memoryUsage"].doubleValue,
            requestCount: json["requestCount"].intValue,
            errorRate: json["errorRate"].doubleValue,
            responseTime: json["responseTime"].doubleValue
        )
    }
    
    private func parseDockerContainer(from json: JSON) -> DockerContainer? {
        guard let id = json["id"].string,
              let name = json["name"].string,
              let image = json["image"].string else {
            return nil
        }
        
        let statusString = json["status"].stringValue
        let status = ContainerStatus(rawValue: statusString) ?? .exited
        
        let state = json["state"].stringValue
        
        let ports = json["ports"].arrayValue.compactMap { portJson in
            parsePortMapping(from: portJson)
        }
        
        let createdString = json["created"].stringValue
        let created = ISO8601DateFormatter().date(from: createdString) ?? Date()
        
        let startedString = json["started"].string
        let started = startedString.flatMap { ISO8601DateFormatter().date(from: $0) }
        
        let networks = json["networks"].arrayValue.map { $0.stringValue }
        
        let volumes = json["volumes"].arrayValue.compactMap { volumeJson in
            parseVolumeMount(from: volumeJson)
        }
        
        let environment = json["environment"].dictionaryValue.mapValues { $0.stringValue }
        let labels = json["labels"].dictionaryValue.mapValues { $0.stringValue }
        
        let stats = parseContainerStats(from: json["stats"])
        
        return DockerContainer(
            id: id,
            name: name,
            image: image,
            status: status,
            state: state,
            ports: ports,
            created: created,
            started: started,
            networks: networks,
            volumes: volumes,
            environment: environment,
            labels: labels,
            stats: stats
        )
    }
    
    private func parsePortMapping(from json: JSON) -> PortMapping? {
        guard let privatePort = json["privatePort"].int else { return nil }
        
        return PortMapping(
            privatePort: privatePort,
            publicPort: json["publicPort"].int,
            type: json["type"].stringValue,
            ip: json["ip"].string
        )
    }
    
    private func parseVolumeMount(from json: JSON) -> VolumeMount? {
        guard let source = json["source"].string,
              let destination = json["destination"].string else {
            return nil
        }
        
        return VolumeMount(
            source: source,
            destination: destination,
            mode: json["mode"].stringValue,
            rw: json["rw"].boolValue
        )
    }
    
    private func parseContainerStats(from json: JSON) -> ContainerStats? {
        guard json.exists() else { return nil }
        
        return ContainerStats(
            cpuPercent: json["cpuPercent"].doubleValue,
            memoryUsage: json["memoryUsage"].int64Value,
            memoryLimit: json["memoryLimit"].int64Value,
            networkRx: json["networkRx"].int64Value,
            networkTx: json["networkTx"].int64Value,
            blockRead: json["blockRead"].int64Value,
            blockWrite: json["blockWrite"].int64Value,
            pids: json["pids"].intValue
        )
    }
    
    private func parseSystemMetrics(from json: JSON) -> SystemMetrics {
        let cpu = CPUMetrics(
            usage: json["cpu"]["usage"].doubleValue,
            cores: json["cpu"]["cores"].intValue,
            frequency: json["cpu"]["frequency"].double,
            temperature: json["cpu"]["temperature"].double
        )
        
        let memory = MemoryMetrics(
            total: json["memory"]["total"].int64Value,
            used: json["memory"]["used"].int64Value,
            free: json["memory"]["free"].int64Value,
            available: json["memory"]["available"].int64Value,
            cached: json["memory"]["cached"].int64Value,
            buffers: json["memory"]["buffers"].int64Value,
            swapTotal: json["memory"]["swapTotal"].int64Value,
            swapUsed: json["memory"]["swapUsed"].int64Value
        )
        
        let disk = DiskMetrics(
            total: json["disk"]["total"].int64Value,
            used: json["disk"]["used"].int64Value,
            free: json["disk"]["free"].int64Value,
            readBytes: json["disk"]["readBytes"].int64Value,
            writeBytes: json["disk"]["writeBytes"].int64Value,
            readOps: json["disk"]["readOps"].int64Value,
            writeOps: json["disk"]["writeOps"].int64Value
        )
        
        let network = NetworkMetrics(
            bytesReceived: json["network"]["bytesReceived"].int64Value,
            bytesSent: json["network"]["bytesSent"].int64Value,
            packetsReceived: json["network"]["packetsReceived"].int64Value,
            packetsSent: json["network"]["packetsSent"].int64Value,
            errorsReceived: json["network"]["errorsReceived"].int64Value,
            errorsSent: json["network"]["errorsSent"].int64Value,
            downloadBytesPerSec: json["network"]["downloadBytesPerSec"].int64Value,
            uploadBytesPerSec: json["network"]["uploadBytesPerSec"].int64Value,
            errors: json["network"]["errors"].int64Value,
            drops: json["network"]["drops"].int64Value
        )
        
        let uptime = json["uptime"].doubleValue
        let loadAverage = json["loadAverage"].arrayValue.map { $0.doubleValue }
        
        let timestampString = json["timestamp"].stringValue
        let timestamp = ISO8601DateFormatter().date(from: timestampString) ?? Date()
        
        return SystemMetrics(
            cpu: cpu,
            memory: memory,
            disk: disk,
            network: network,
            uptime: uptime,
            loadAverage: loadAverage,
            processes: json["processes"].intValue,
            runningProcesses: json["runningProcesses"].intValue,
            sleepingProcesses: json["sleepingProcesses"].intValue,
            zombieProcesses: json["zombieProcesses"].intValue,
            timestamp: timestamp,
            bootTime: ISO8601DateFormatter().date(from: json["bootTime"].stringValue) ?? Date()
        )
    }
}

// MARK: - API Errors
enum APIError: Error, LocalizedError {
    case networkError(Error)
    case invalidResponse
    case serverError(String)
    case unauthorized
    case notFound
    
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .invalidResponse:
            return "Invalid response from server"
        case .serverError(let message):
            return "Server error: \(message)"
        case .unauthorized:
            return "Unauthorized access"
        case .notFound:
            return "Resource not found"
        }
    }
}