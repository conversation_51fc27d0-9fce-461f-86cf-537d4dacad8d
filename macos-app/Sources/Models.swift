import Foundation
import SwiftUI

// MARK: - System Models

struct SystemMetrics: Codable, Identifiable {
    let id = UUID()
    let timestamp: Date
    let cpu: CPUMetrics
    let memory: MemoryMetrics
    let disk: DiskMetrics
    let network: NetworkMetrics
}

struct CPUMetrics: Codable {
    let usage: Double
    let cores: Int
    let loadAverage: [Double]
    let temperature: Double?
}

struct MemoryMetrics: Codable {
    let total: Int64
    let used: Int64
    let available: Int64
    let cached: Int64
    let buffers: Int64
    
    var usagePercentage: Double {
        return Double(used) / Double(total) * 100
    }
}

struct DiskMetrics: Codable {
    let total: Int64
    let used: Int64
    let available: Int64
    let readOperations: Int64
    let writeOperations: Int64
    let readBytes: Int64
    let writeBytes: Int64
    
    var usagePercentage: Double {
        return Double(used) / Double(total) * 100
    }
}

struct NetworkMetrics: Codable {
    let bytesReceived: Int64
    let bytesSent: Int64
    let packetsReceived: Int64
    let packetsSent: Int64
    let errors: Int64
}

// MARK: - Service Models

struct ServiceInfo: Codable, Identifiable {
    let id: String
    let name: String
    let status: ServiceStatus
    let port: Int?
    let uptime: TimeInterval?
    let description: String?
    let version: String?
    let dependencies: [String]
    let resourceUsage: ResourceUsage?
    let lastUpdated: Date
}

enum ServiceStatus: String, Codable, CaseIterable {
    case running = "running"
    case stopped = "stopped"
    case starting = "starting"
    case stopping = "stopping"
    case error = "error"
    case unknown = "unknown"
    
    var color: Color {
        switch self {
        case .running:
            return .green
        case .stopped:
            return .gray
        case .starting, .stopping:
            return .orange
        case .error:
            return .red
        case .unknown:
            return .yellow
        }
    }
    
    var systemImage: String {
        switch self {
        case .running:
            return "checkmark.circle.fill"
        case .stopped:
            return "stop.circle.fill"
        case .starting:
            return "arrow.clockwise.circle.fill"
        case .stopping:
            return "arrow.counterclockwise.circle.fill"
        case .error:
            return "exclamationmark.triangle.fill"
        case .unknown:
            return "questionmark.circle.fill"
        }
    }
}

struct ResourceUsage: Codable {
    let cpu: Double
    let memory: Int64
    let diskRead: Int64
    let diskWrite: Int64
    let networkIn: Int64
    let networkOut: Int64
}

// MARK: - Docker Models

struct DockerContainer: Codable, Identifiable {
    let id: String
    let name: String
    let image: String
    let status: ContainerStatus
    let state: String
    let ports: [PortMapping]
    let volumes: [VolumeMapping]
    let environment: [String: String]
    let created: Date
    let started: Date?
    let stats: ContainerStats?
}

enum ContainerStatus: String, Codable, CaseIterable {
    case running = "running"
    case exited = "exited"
    case created = "created"
    case restarting = "restarting"
    case removing = "removing"
    case paused = "paused"
    case dead = "dead"
    
    var color: Color {
        switch self {
        case .running:
            return .green
        case .exited, .dead:
            return .red
        case .created:
            return .blue
        case .restarting:
            return .orange
        case .removing:
            return .purple
        case .paused:
            return .yellow
        }
    }
    
    var systemImage: String {
        switch self {
        case .running:
            return "play.circle.fill"
        case .exited, .dead:
            return "stop.circle.fill"
        case .created:
            return "plus.circle.fill"
        case .restarting:
            return "arrow.clockwise.circle.fill"
        case .removing:
            return "trash.circle.fill"
        case .paused:
            return "pause.circle.fill"
        }
    }
}

struct PortMapping: Codable {
    let hostPort: Int
    let containerPort: Int
    let protocol: String
}

struct VolumeMapping: Codable {
    let hostPath: String
    let containerPath: String
    let mode: String
}

struct ContainerStats: Codable {
    let cpu: Double
    let memory: Int64
    let memoryLimit: Int64
    let networkIn: Int64
    let networkOut: Int64
    let blockRead: Int64
    let blockWrite: Int64
    
    var memoryUsagePercentage: Double {
        return memoryLimit > 0 ? Double(memory) / Double(memoryLimit) * 100 : 0
    }
}

struct DockerImage: Codable, Identifiable {
    let id: String
    let repository: String
    let tag: String
    let size: Int64
    let created: Date
    let containers: Int
}

// MARK: - Log Models

struct LogEntry: Codable, Identifiable {
    let id = UUID()
    let timestamp: Date
    let level: LogLevel
    let source: String
    let message: String
    let metadata: [String: String]?
}

enum LogLevel: String, Codable, CaseIterable {
    case debug = "debug"
    case info = "info"
    case warning = "warning"
    case error = "error"
    case critical = "critical"
    
    var color: Color {
        switch self {
        case .debug:
            return .secondary
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        case .critical:
            return .purple
        }
    }
    
    var systemImage: String {
        switch self {
        case .debug:
            return "ant.circle.fill"
        case .info:
            return "info.circle.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .error:
            return "xmark.circle.fill"
        case .critical:
            return "exclamationmark.octagon.fill"
        }
    }
}

// MARK: - Notification Models

struct AppNotification: Codable, Identifiable {
    let id = UUID()
    let title: String
    let message: String
    let type: NotificationType
    let timestamp: Date
    let read: Bool
    let actions: [NotificationAction]?
}

enum NotificationType: String, Codable, CaseIterable {
    case info = "info"
    case warning = "warning"
    case error = "error"
    case success = "success"
    
    var color: Color {
        switch self {
        case .info:
            return .blue
        case .warning:
            return .orange
        case .error:
            return .red
        case .success:
            return .green
        }
    }
    
    var systemImage: String {
        switch self {
        case .info:
            return "info.circle.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .error:
            return "xmark.circle.fill"
        case .success:
            return "checkmark.circle.fill"
        }
    }
}

struct NotificationAction: Codable {
    let id: String
    let title: String
    let action: String
}

// MARK: - Settings Models

struct AppSettings: Codable {
    var connection: ConnectionSettings
    var notifications: NotificationSettings
    var appearance: AppearanceSettings
    var monitoring: MonitoringSettings
    var shortcuts: ShortcutSettings
    var advanced: AdvancedSettings
}

struct ConnectionSettings: Codable {
    var serverURL: String
    var apiKey: String?
    var websocketURL: String?
    var autoConnect: Bool
    var connectionTimeout: TimeInterval
    var retryAttempts: Int
}

struct NotificationSettings: Codable {
    var systemAlerts: Bool
    var serviceEvents: Bool
    var dockerEvents: Bool
    var errorAlerts: Bool
    var soundEnabled: Bool
    var badgeEnabled: Bool
}

struct AppearanceSettings: Codable {
    var colorScheme: AppColorScheme
    var accentColor: String
    var showMenuBar: Bool
    var compactMode: Bool
}

enum AppColorScheme: String, Codable, CaseIterable {
    case system = "system"
    case light = "light"
    case dark = "dark"
}

struct MonitoringSettings: Codable {
    var refreshInterval: TimeInterval
    var historyRetention: TimeInterval
    var alertThresholds: AlertThresholds
    var enableRealtime: Bool
}

struct AlertThresholds: Codable {
    var cpuWarning: Double
    var cpuCritical: Double
    var memoryWarning: Double
    var memoryCritical: Double
    var diskWarning: Double
    var diskCritical: Double
}

struct ShortcutSettings: Codable {
    var quickSearch: String
    var refreshAll: String
    var connectServer: String
    var checkUpdates: String
    var showMenuBar: String
}

struct AdvancedSettings: Codable {
    var debugMode: Bool
    var logLevel: LogLevel
    var enableAnalytics: Bool
    var exportFormat: ExportFormat
}

enum ExportFormat: String, Codable, CaseIterable {
    case json = "json"
    case csv = "csv"
    case pdf = "pdf"
}

// MARK: - API Response Models

struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let error: String?
    let timestamp: Date
}

struct PaginatedResponse<T: Codable>: Codable {
    let items: [T]
    let page: Int
    let pageSize: Int
    let totalItems: Int
    let totalPages: Int
}

// MARK: - WebSocket Models

struct WebSocketMessage: Codable {
    let type: MessageType
    let payload: Data
    let timestamp: Date
}

enum MessageType: String, Codable {
    case systemMetrics = "system_metrics"
    case serviceUpdate = "service_update"
    case dockerUpdate = "docker_update"
    case logEntry = "log_entry"
    case notification = "notification"
    case error = "error"
}

// MARK: - Search Models

struct SearchResult: Identifiable {
    let id = UUID()
    let title: String
    let subtitle: String?
    let category: SearchCategory
    let action: () -> Void
}

enum SearchCategory: String, CaseIterable {
    case services = "Services"
    case containers = "Containers"
    case logs = "Logs"
    case settings = "Settings"
    case actions = "Actions"
    
    var systemImage: String {
        switch self {
        case .services:
            return "gear"
        case .containers:
            return "shippingbox"
        case .logs:
            return "doc.text"
        case .settings:
            return "slider.horizontal.3"
        case .actions:
            return "bolt"
        }
    }
}