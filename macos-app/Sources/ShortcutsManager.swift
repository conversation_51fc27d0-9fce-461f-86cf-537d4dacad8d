import Foundation
import Intents
import IntentsUI
import AppIntents
import SwiftUI

// MARK: - App Intents

@available(macOS 13.0, *)
struct GetServerStatusIntent: AppIntent {
    static var title: LocalizedStringResource = "Get Server Status"
    static var description = IntentDescription("Get the current status of the WOW server and services")
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        let runningServices = appState.services.filter { $0.status == .running }.count
        let totalServices = appState.services.count
        let runningContainers = appState.dockerContainers.filter { $0.state == "running" }.count
        let totalContainers = appState.dockerContainers.count
        
        let status = appState.isConnected ? "Connected" : "Disconnected"
        
        let message = """
        Server Status: \(status)
        Services: \(runningServices)/\(totalServices) running
        Containers: \(runningContainers)/\(totalContainers) running
        """
        
        return .result(dialog: IntentDialog(stringLiteral: message))
    }
}

@available(macOS 13.0, *)
struct RestartServiceIntent: AppIntent {
    static var title: LocalizedStringResource = "Restart Service"
    static var description = IntentDescription("Restart a specific service")
    
    @Parameter(title: "Service Name")
    var serviceName: String
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        guard let service = appState.services.first(where: { $0.name == serviceName }) else {
            return .result(dialog: IntentDialog("Service '\(serviceName)' not found"))
        }
        
        do {
            await appState.restartService(serviceName)
            return .result(dialog: IntentDialog("Service '\(serviceName)' restarted successfully"))
        } catch {
            return .result(dialog: IntentDialog("Failed to restart service '\(serviceName)': \(error.localizedDescription)"))
        }
    }
}

@available(macOS 13.0, *)
struct StartServiceIntent: AppIntent {
    static var title: LocalizedStringResource = "Start Service"
    static var description = IntentDescription("Start a specific service")
    
    @Parameter(title: "Service Name")
    var serviceName: String
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        guard let service = appState.services.first(where: { $0.name == serviceName }) else {
            return .result(dialog: IntentDialog("Service '\(serviceName)' not found"))
        }
        
        if service.status == .running {
            return .result(dialog: IntentDialog("Service '\(serviceName)' is already running"))
        }
        
        do {
            await appState.startService(serviceName)
            return .result(dialog: IntentDialog("Service '\(serviceName)' started successfully"))
        } catch {
            return .result(dialog: IntentDialog("Failed to start service '\(serviceName)': \(error.localizedDescription)"))
        }
    }
}

@available(macOS 13.0, *)
struct StopServiceIntent: AppIntent {
    static var title: LocalizedStringResource = "Stop Service"
    static var description = IntentDescription("Stop a specific service")
    
    @Parameter(title: "Service Name")
    var serviceName: String
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        guard let service = appState.services.first(where: { $0.name == serviceName }) else {
            return .result(dialog: IntentDialog("Service '\(serviceName)' not found"))
        }
        
        if service.status == .stopped {
            return .result(dialog: IntentDialog("Service '\(serviceName)' is already stopped"))
        }
        
        do {
            await appState.stopService(serviceName)
            return .result(dialog: IntentDialog("Service '\(serviceName)' stopped successfully"))
        } catch {
            return .result(dialog: IntentDialog("Failed to stop service '\(serviceName)': \(error.localizedDescription)"))
        }
    }
}

@available(macOS 13.0, *)
struct GetSystemMetricsIntent: AppIntent {
    static var title: LocalizedStringResource = "Get System Metrics"
    static var description = IntentDescription("Get current system performance metrics")
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        guard let metrics = appState.systemMetrics else {
            return .result(dialog: IntentDialog("System metrics not available"))
        }
        
        let message = """
        CPU Usage: \(String(format: "%.1f", metrics.cpu.usage))%
        Memory Usage: \(String(format: "%.1f", Double(metrics.memory.used) / Double(metrics.memory.total) * 100))%
        Disk Usage: \(String(format: "%.1f", Double(metrics.disk.used) / Double(metrics.disk.total) * 100))%
        Load Average: \(String(format: "%.2f", metrics.loadAverage.oneMinute))
        """
        
        return .result(dialog: IntentDialog(message))
    }
}

@available(macOS 13.0, *)
struct StartAllServicesIntent: AppIntent {
    static var title: LocalizedStringResource = "Start All Services"
    static var description = IntentDescription("Start all stopped services")
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        let stoppedServices = appState.services.filter { $0.status == .stopped }
        
        if stoppedServices.isEmpty {
            return .result(dialog: IntentDialog("All services are already running"))
        }
        
        var successCount = 0
        var failedServices: [String] = []
        
        for service in stoppedServices {
            do {
                await appState.startService(service.name)
                successCount += 1
            } catch {
                failedServices.append(service.name)
            }
        }
        
        if failedServices.isEmpty {
            return .result(dialog: IntentDialog("Successfully started \(successCount) services"))
        } else {
            let message = "Started \(successCount) services. Failed to start: \(failedServices.joined(separator: ", "))"
            return .result(dialog: IntentDialog(message))
        }
    }
}

@available(macOS 13.0, *)
struct StopAllServicesIntent: AppIntent {
    static var title: LocalizedStringResource = "Stop All Services"
    static var description = IntentDescription("Stop all running services")
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        let runningServices = appState.services.filter { $0.status == .running }
        
        if runningServices.isEmpty {
            return .result(dialog: IntentDialog("No services are currently running"))
        }
        
        var successCount = 0
        var failedServices: [String] = []
        
        for service in runningServices {
            do {
                await appState.stopService(service.name)
                successCount += 1
            } catch {
                failedServices.append(service.name)
            }
        }
        
        if failedServices.isEmpty {
            return .result(dialog: IntentDialog("Successfully stopped \(successCount) services"))
        } else {
            let message = "Stopped \(successCount) services. Failed to stop: \(failedServices.joined(separator: ", "))"
            return .result(dialog: IntentDialog(message))
        }
    }
}

@available(macOS 13.0, *)
struct GetDockerStatusIntent: AppIntent {
    static var title: LocalizedStringResource = "Get Docker Status"
    static var description = IntentDescription("Get the status of Docker containers")
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        let runningContainers = appState.dockerContainers.filter { $0.state == "running" }
        let stoppedContainers = appState.dockerContainers.filter { $0.state == "exited" }
        let totalContainers = appState.dockerContainers.count
        
        let message = """
        Docker Containers:
        Total: \(totalContainers)
        Running: \(runningContainers.count)
        Stopped: \(stoppedContainers.count)
        """
        
        return .result(dialog: IntentDialog(message))
    }
}

@available(macOS 13.0, *)
struct RestartContainerIntent: AppIntent {
    static var title: LocalizedStringResource = "Restart Container"
    static var description = IntentDescription("Restart a specific Docker container")
    
    @Parameter(title: "Container Name")
    var containerName: String
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let appState = AppState.shared
        
        guard let container = appState.dockerContainers.first(where: { $0.name == containerName }) else {
            return .result(dialog: IntentDialog("Container '\(containerName)' not found"))
        }
        
        do {
            // Implementation would call Docker API to restart container
            return .result(dialog: IntentDialog("Container '\(containerName)' restarted successfully"))
        } catch {
            return .result(dialog: IntentDialog("Failed to restart container '\(containerName)': \(error.localizedDescription)"))
        }
    }
}

// MARK: - Shortcuts Manager

@MainActor
class ShortcutsManager: ObservableObject {
    static let shared = ShortcutsManager()
    
    @Published var availableIntents: [any AppIntent.Type] = []
    @Published var donatedShortcuts: [INVoiceShortcut] = []
    
    private init() {
        setupAvailableIntents()
    }
    
    func initialize() async {
        setupAvailableIntents()
        await loadDonatedShortcuts()
    }
    
    private func loadDonatedShortcuts() async {
        // Load donated shortcuts - implementation can be added later
        donatedShortcuts = []
    }
    
    private func setupAvailableIntents() {
        if #available(macOS 13.0, *) {
            availableIntents = [
                GetServerStatusIntent.self,
                RestartServiceIntent.self,
                StartServiceIntent.self,
                StopServiceIntent.self,
                GetSystemMetricsIntent.self,
                StartAllServicesIntent.self,
                StopAllServicesIntent.self,
                GetDockerStatusIntent.self,
                RestartContainerIntent.self
            ]
        }
    }
    
    // MARK: - Shortcut Donation
    
    func donateShortcuts() {
        if #available(macOS 13.0, *) {
            // Donate common shortcuts for better Siri suggestions
            donateGetServerStatusShortcut()
            donateRestartServiceShortcut()
            donateGetMetricsShortcut()
        }
    }
    
    @available(macOS 13.0, *)
    private func donateGetServerStatusShortcut() {
        let intent = GetServerStatusIntent()
        let shortcut = INShortcut(intent: intent)
        
        let voiceShortcut = INVoiceShortcut(shortcut: shortcut, invocationPhrase: "Get WOW server status")
        
        INVoiceShortcutCenter.shared.setShortcutSuggestions([voiceShortcut])
    }
    
    @available(macOS 13.0, *)
    private func donateRestartServiceShortcut() {
        // This would typically be called when user performs the action
        let intent = RestartServiceIntent()
        intent.serviceName = "example-service"
        
        let interaction = INInteraction(intent: intent, response: nil)
        interaction.donate { error in
            if let error = error {
                print("Failed to donate restart service shortcut: \(error)")
            }
        }
    }
    
    @available(macOS 13.0, *)
    private func donateGetMetricsShortcut() {
        let intent = GetSystemMetricsIntent()
        let shortcut = INShortcut(intent: intent)
        
        let voiceShortcut = INVoiceShortcut(shortcut: shortcut, invocationPhrase: "Get system metrics")
        
        INVoiceShortcutCenter.shared.setShortcutSuggestions([voiceShortcut])
    }
    
    // MARK: - Shortcut Management
    
    func getVoiceShortcuts() async -> [INVoiceShortcut] {
        do {
            return try await INVoiceShortcutCenter.shared.allVoiceShortcuts()
        } catch {
            print("Failed to get voice shortcuts: \(error)")
            return []
        }
    }
    
    func addVoiceShortcut(for intent: any AppIntent, phrase: String) {
        if #available(macOS 13.0, *) {
            // Implementation for adding voice shortcuts
            // This would typically open the Shortcuts app or system dialog
        }
    }
    
    func removeVoiceShortcut(_ shortcut: INVoiceShortcut) async {
        // Note: INVoiceShortcutCenter doesn't have a direct removeVoiceShortcut method
        // Voice shortcuts are typically managed through the Shortcuts app
        print("Voice shortcut removal should be handled through the Shortcuts app")
    }
    
    // MARK: - Suggested Shortcuts
    
    func updateShortcutSuggestions(basedOn context: ShortcutContext) {
        if #available(macOS 13.0, *) {
            var suggestions: [INShortcut] = []
            
            switch context {
            case .serviceManagement:
                suggestions.append(contentsOf: getServiceManagementShortcuts())
            case .systemMonitoring:
                suggestions.append(contentsOf: getSystemMonitoringShortcuts())
            case .dockerManagement:
                suggestions.append(contentsOf: getDockerManagementShortcuts())
            case .general:
                suggestions.append(contentsOf: getGeneralShortcuts())
            }
            
            let voiceShortcuts = suggestions.compactMap { shortcut -> INVoiceShortcut? in
            return INVoiceShortcut(shortcut: shortcut, invocationPhrase: shortcut.intent?.suggestedInvocationPhrase ?? "")
        }
        INVoiceShortcutCenter.shared.setShortcutSuggestions(voiceShortcuts)
        }
    }
    
    @available(macOS 13.0, *)
    private func getServiceManagementShortcuts() -> [INShortcut] {
        return [
            INShortcut(intent: StartAllServicesIntent()),
            INShortcut(intent: StopAllServicesIntent()),
            INShortcut(intent: RestartServiceIntent())
        ]
    }
    
    @available(macOS 13.0, *)
    private func getSystemMonitoringShortcuts() -> [INShortcut] {
        // For AppIntents, we need to create shortcuts differently
        // These will be handled by the App Intents framework
        return []
    }
    
    @available(macOS 13.0, *)
    private func getDockerManagementShortcuts() -> [INShortcut] {
        // For AppIntents, we need to create shortcuts differently
        // These will be handled by the App Intents framework
        return []
    }
    
    @available(macOS 13.0, *)
    private func getGeneralShortcuts() -> [INShortcut] {
        // For AppIntents, we need to create shortcuts differently
        // These will be handled by the App Intents framework
        return []
    }
}

// MARK: - Supporting Types

enum ShortcutContext {
    case serviceManagement
    case systemMonitoring
    case dockerManagement
    case general
}

// MARK: - Shortcut Configuration View

struct ShortcutsConfigurationView: View {
    @StateObject private var shortcutsManager = ShortcutsManager.shared
    @State private var voiceShortcuts: [INVoiceShortcut] = []
    @State private var isLoading = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Shortcuts Integration")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Configure voice shortcuts and Siri integration for WOW Monitor.")
                .foregroundColor(.secondary)
            
            Divider()
            
            // Available Intents
            VStack(alignment: .leading, spacing: 8) {
                Text("Available Actions")
                    .font(.headline)
                
                if #available(macOS 13.0, *) {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        ForEach(shortcutsManager.availableIntents.indices, id: \.self) { index in
                            let intentType = shortcutsManager.availableIntents[index]
                            ShortcutActionCard(intentType: intentType)
                        }
                    }
                } else {
                    Text("Shortcuts integration requires macOS 13.0 or later")
                        .foregroundColor(.secondary)
                }
            }
            
            Divider()
            
            // Voice Shortcuts
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Voice Shortcuts")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button("Refresh") {
                        loadVoiceShortcuts()
                    }
                    .buttonStyle(.bordered)
                    .disabled(isLoading)
                }
                
                if isLoading {
                    ProgressView("Loading shortcuts...")
                        .frame(maxWidth: .infinity)
                } else if voiceShortcuts.isEmpty {
                    Text("No voice shortcuts configured")
                        .foregroundColor(.secondary)
                } else {
                    ForEach(voiceShortcuts.indices, id: \.self) { index in
                        VoiceShortcutRow(shortcut: voiceShortcuts[index]) {
                            removeShortcut(at: index)
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding()
        .onAppear {
            loadVoiceShortcuts()
        }
    }
    
    private func loadVoiceShortcuts() {
        isLoading = true
        Task {
            let shortcuts = await shortcutsManager.getVoiceShortcuts()
            await MainActor.run {
                voiceShortcuts = shortcuts
                isLoading = false
            }
        }
    }
    
    private func removeShortcut(at index: Int) {
        let shortcut = voiceShortcuts[index]
        Task {
            await shortcutsManager.removeVoiceShortcut(shortcut)
            await MainActor.run {
                voiceShortcuts.remove(at: index)
            }
        }
    }
}

struct ShortcutActionCard: View {
    let intentType: any AppIntent.Type
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: iconForIntent(intentType))
                    .foregroundColor(.accentColor)
                
                Spacer()
                
                Button("Add") {
                    // Implementation for adding shortcut
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
            
            Text(titleForIntent(intentType))
                .font(.subheadline)
                .fontWeight(.medium)
            
            Text(descriptionForIntent(intentType))
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(2)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
    
    private func iconForIntent(_ intentType: any AppIntent.Type) -> String {
        let typeName = String(describing: intentType)
        switch typeName {
        case let name where name.contains("Server"):
            return "server.rack"
        case let name where name.contains("Service"):
            return "gear"
        case let name where name.contains("Docker"):
            return "cube"
        case let name where name.contains("Metrics"):
            return "chart.bar"
        default:
            return "app"
        }
    }
    
    private func titleForIntent(_ intentType: any AppIntent.Type) -> String {
        // Extract title from intent type
        return String(describing: intentType).replacingOccurrences(of: "Intent", with: "")
    }
    
    private func descriptionForIntent(_ intentType: any AppIntent.Type) -> String {
        // Return appropriate description based on intent type
        return "Shortcut action for \(titleForIntent(intentType))"
    }
}

struct VoiceShortcutRow: View {
    let shortcut: INVoiceShortcut
    let onRemove: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(shortcut.invocationPhrase)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                if let shortcutName = shortcut.shortcut.intent?.suggestedInvocationPhrase {
                    Text(shortcutName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Button("Remove") {
                onRemove()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
            .foregroundColor(.red)
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    ShortcutsConfigurationView()
        .frame(width: 600, height: 500)
}