import React, { useState } from 'react';
import { Grid, Stack, Group, Card as MantineCard, Text, Badge as MantineBadge, <PERSON><PERSON> as MantineButton } from '@mantine/core';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { MetricCard } from '~/components/ui/enhanced-card';
import { ResponsiveStack, ResponsiveShow, useBreakpoint } from '~/components/ui/responsive-layout';
import { LoadingOverlay } from '~/components/ui/loading-states';
import { But<PERSON> } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import { cn, typography, layout, animations, states } from '~/lib/ui-utils';
import { microinteractions } from '~/lib/microinteractions';
import {
  Library,
  Plus,
  Search,
  Network,
  FolderOpen,
  Sparkles,
  Users,
  Filter,
  Brain,
  Link2,
  Tag,
  TrendingUp,
} from 'lucide-react';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { SmartCollections } from './SmartCollections';
import { KnowledgeGraph } from './KnowledgeGraph';
import { QuickCapture } from './QuickCapture';

export function KnowledgeHub() {
  const [activeTab, setActiveTab] = useState('collections');
  const [searchQuery, setSearchQuery] = useState('');
  const {
    currentProject,
    knowledgeCollections,
    knowledgeGraph,
    learningEvents,
    insights,
  } = useLighthouseStore();

  if (!currentProject) return null;

  // Calculate knowledge metrics
  const totalConcepts = currentProject.intelligence.domainExpertise.concepts.length;
  const totalConnections = knowledgeGraph?.edges.length || 0;
  const recentLearning = learningEvents.filter(
    e => e.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
  ).length;

  const breakpoint = useBreakpoint();
  const isMobile = breakpoint === 'mobile';
  const isTablet = breakpoint === 'tablet';

  return (
    <LoadingOverlay loading={false} className="h-full">
      <ResponsiveStack 
        direction={{ default: 'column', lg: 'row' }}
        className="h-full"
        gap="none"
      >
        {/* Knowledge Overview Sidebar */}
        <ResponsiveShow above="lg">
          <aside className={cn(
            'w-80 border-r bg-muted/50 p-6',
            layout.spaceY.lg,
            'transition-all duration-200'
          )}>
            {/* Quick Stats */}
            <div className={layout.spaceY.md}>
              <h3 className={cn(typography.h3, 'flex items-center gap-2')}>
                <Brain className="h-5 w-5 text-primary" />
                Knowledge Overview
              </h3>
              
              <Grid gutter="sm">
                <Grid.Col span={6}>
                  <MetricCard
                    title="Concepts"
                    value={totalConcepts}
                    icon={<Brain className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <MetricCard
                    title="Connections"
                    value={totalConnections}
                    icon={<Link2 className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <MetricCard
                    title="Collections"
                    value={knowledgeCollections.length}
                    icon={<FolderOpen className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <MetricCard
                    title="Today"
                    value={`+${recentLearning}`}
                    icon={<TrendingUp className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
              </Grid>
            </div>

            {/* Quick Capture */}
            <QuickCapture />

            {/* Top Concepts */}
            <Card className={cn(microinteractions.card.subtle)}>
              <CardHeader className="pb-3">
                <CardTitle className={cn(typography.h5, 'flex items-center gap-2')}>
                  <TrendingUp className="h-4 w-4 text-primary" />
                  Top Concepts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={layout.spaceY.sm}>
                  {currentProject.intelligence.domainExpertise.concepts
                    .slice(0, 5)
                    .map((concept) => (
                      <div
                        key={concept.id}
                        className={cn(
                          layout.flexBetween,
                          typography.bodySmall,
                          'p-2 rounded-md transition-colors duration-150',
                          'hover:bg-accent/50 cursor-pointer',
                          microinteractions.hover.bgSubtle
                        )}
                      >
                        <span className="truncate">{concept.name}</span>
                        <Badge 
                          variant="secondary" 
                          className={cn(
                            'text-xs transition-all duration-150',
                            microinteractions.hover.scaleUpSm
                          )}
                        >
                          {Math.round(concept.confidence * 100)}%
                        </Badge>
                      </div>
                    ))}
                  {currentProject.intelligence.domainExpertise.concepts.length === 0 && (
                    <p className={cn(typography.caption, 'text-center py-4')}>
                      No concepts learned yet
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Insights */}
            <Card className={cn(microinteractions.card.subtle)}>
              <CardHeader className="pb-3">
                <CardTitle className={cn(typography.h5, 'flex items-center gap-2')}>
                  <Sparkles className="h-4 w-4 text-primary" />
                  Recent Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-32">
                  <div className={layout.spaceY.sm}>
                    {insights
                      .filter(i => i.connections.length > 0)
                      .slice(0, 3)
                      .map((insight) => (
                        <div
                          key={insight.id}
                          className={cn(
                            typography.bodySmall,
                            'p-2 rounded-lg bg-muted transition-all duration-200',
                            'hover:bg-muted/80 cursor-pointer',
                            microinteractions.hover.lift,
                            microinteractions.focus.ring
                          )}
                          role="button"
                          tabIndex={0}
                        >
                          <p className="line-clamp-2">{insight.content}</p>
                          <div className={cn(layout.flexStart, 'gap-2 mt-1')}>
                            <Link2 className="h-3 w-3 text-muted-foreground" />
                            <span className={typography.caption}>
                              {insight.connections.length} connections
                            </span>
                          </div>
                        </div>
                      ))}
                    {insights.filter(i => i.connections.length > 0).length === 0 && (
                      <p className={cn(typography.caption, 'text-center py-4')}>
                        No connected insights yet
                      </p>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </aside>
        </ResponsiveShow>

        {/* Main Content */}
        <main className={cn(
          'flex-1 p-4 md:p-6',
          'transition-all duration-200'
        )}>
          {/* Mobile Stats - Only show on mobile */}
          <ResponsiveShow below="lg">
            <div className={cn(layout.spaceY.md, 'mb-6')}>
              <h2 className={cn(typography.h2, 'flex items-center gap-2')}>
                <Brain className="h-6 w-6 text-primary" />
                Knowledge Hub
              </h2>
              
              <Grid gutter="sm">
                <Grid.Col span={{ base: 6, sm: 3 }}>
                  <MetricCard
                    title="Concepts"
                    value={totalConcepts}
                    icon={<Brain className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 6, sm: 3 }}>
                  <MetricCard
                    title="Connections"
                    value={totalConnections}
                    icon={<Link2 className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 6, sm: 3 }}>
                  <MetricCard
                    title="Collections"
                    value={knowledgeCollections.length}
                    icon={<FolderOpen className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 6, sm: 3 }}>
                  <MetricCard
                    title="Today"
                    value={`+${recentLearning}`}
                    icon={<TrendingUp className="h-4 w-4" />}
                    className={microinteractions.card.subtle}
                  />
                </Grid.Col>
              </Grid>
            </div>
          </ResponsiveShow>

          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative max-w-2xl mx-auto lg:mx-0">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search knowledge base..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={cn(
                  'pl-10 pr-16 transition-all duration-200',
                  microinteractions.input.standard,
                  'focus:shadow-md'
                )}
              />
              <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
                <Button 
                  size="sm" 
                  variant="ghost"
                  className={microinteractions.button.ghost}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Content Tabs */}
          <Tabs defaultValue="collections" value={activeTab} onChange={(value) => {
            if (value) setActiveTab(value);
          }}>
            <TabsList className={cn(
              'mb-6 grid w-full',
              isMobile ? 'grid-cols-2' : isTablet ? 'grid-cols-2' : 'grid-cols-4'
            )}>
              <TabsTrigger 
                value="collections" 
                className={cn(
                  'flex items-center gap-2 transition-all duration-200',
                  microinteractions.navigation.tab
                )}
              >
                <FolderOpen className="h-4 w-4" />
                <span className={cn(isMobile ? 'sr-only' : '')}>Collections</span>
              </TabsTrigger>
              <TabsTrigger 
                value="graph" 
                className={cn(
                  'flex items-center gap-2 transition-all duration-200',
                  microinteractions.navigation.tab
                )}
              >
                <Network className="h-4 w-4" />
                <span className={cn(isMobile ? 'sr-only' : '')}>Graph</span>
              </TabsTrigger>
              {!isMobile && (
                <>
                  <TabsTrigger 
                    value="collaborative" 
                    className={cn(
                      'flex items-center gap-2 transition-all duration-200',
                      microinteractions.navigation.tab
                    )}
                  >
                    <Users className="h-4 w-4" />
                    <span className={cn(isTablet ? 'sr-only' : '')}>Collaborative</span>
                  </TabsTrigger>
                  <TabsTrigger 
                    value="tags" 
                    className={cn(
                      'flex items-center gap-2 transition-all duration-200',
                      microinteractions.navigation.tab
                    )}
                  >
                    <Tag className="h-4 w-4" />
                    <span className={cn(isTablet ? 'sr-only' : '')}>Tags</span>
                  </TabsTrigger>
                </>
              )}
            </TabsList>

            <TabsContent value="collections" className="mt-0">
              <SmartCollections searchQuery={searchQuery} />
            </TabsContent>

            <TabsContent value="graph" className="mt-0">
              <KnowledgeGraph searchQuery={searchQuery} />
            </TabsContent>

            {!isMobile && (
              <TabsContent value="collaborative" className="mt-0">
                <Card className={microinteractions.card.subtle}>
                  <CardHeader>
                    <CardTitle className={typography.h3}>Collaborative Knowledge Spaces</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={cn(layout.flexColCenter, 'py-12')}>
                      <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className={cn(typography.h4, 'mb-2')}>
                        Team Knowledge Sharing
                      </h3>
                      <p className={cn(typography.body, 'text-muted-foreground mb-4 max-w-md text-center')}>
                        Create shared knowledge spaces where team members can collaborate
                        on building domain expertise together.
                      </p>
                      <Button className={microinteractions.button.primary}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Team Space
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {!isMobile && (
              <TabsContent value="tags" className="mt-0">
                <Card className={microinteractions.card.subtle}>
                  <CardHeader>
                    <CardTitle className={typography.h3}>Tags & Topics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={layout.spaceY.lg}>
                      {/* Topic Cloud */}
                      <div>
                        <h4 className={cn(typography.h5, 'mb-3')}>Popular Topics</h4>
                        <div className="flex flex-wrap gap-2">
                          {[...currentProject.intelligence.domainExpertise.vocabulary]
                            .sort((a, b) => b.frequency - a.frequency)
                            .slice(0, 20)
                            .map((term) => (
                              <Badge
                                key={term.term}
                                variant="secondary"
                                className={cn(
                                  'cursor-pointer transition-all duration-200',
                                  'hover:bg-secondary/80 hover:scale-105',
                                  microinteractions.hover.scaleUpSm,
                                  microinteractions.focus.ring,
                                  term.frequency > 10 && 'text-base',
                                  term.frequency > 5 && 'text-sm'
                                )}
                                role="button"
                                tabIndex={0}
                              >
                                {term.term} ({term.frequency})
                              </Badge>
                            ))}
                        </div>
                        {currentProject.intelligence.domainExpertise.vocabulary.length === 0 && (
                          <p className={cn(typography.caption, 'text-center py-4')}>
                            No vocabulary terms identified yet. Add sources to build your knowledge base.
                          </p>
                        )}
                      </div>

                      {/* AI-Generated Tags */}
                      <div>
                        <h4 className={cn(typography.h5, 'mb-3')}>AI-Generated Tags</h4>
                        <Grid gutter="md">
                          <Grid.Col span={{ base: 12, md: 6 }}>
                            <Card className={cn('p-4', microinteractions.card.subtle)}>
                              <div className={cn(layout.flexStart, 'gap-2 mb-2')}>
                                <Brain className="h-4 w-4 text-primary" />
                                <span className={typography.label}>Domain Tags</span>
                              </div>
                              <div className="flex flex-wrap gap-1">
                                {currentProject.intelligence.domainExpertise.relatedDomains
                                  .map((domain) => (
                                    <Badge
                                      key={domain}
                                      variant="outline"
                                      className={cn(
                                        'text-xs transition-all duration-150',
                                        microinteractions.hover.scaleUpSm
                                      )}
                                    >
                                      {domain}
                                    </Badge>
                                  ))}
                              </div>
                            </Card>
                          </Grid.Col>
                          <Grid.Col span={{ base: 12, md: 6 }}>
                            <Card className={cn('p-4', microinteractions.card.subtle)}>
                              <div className={cn(layout.flexStart, 'gap-2 mb-2')}>
                                <Sparkles className="h-4 w-4 text-yellow-500" />
                                <span className={typography.label}>Emerging Topics</span>
                              </div>
                              <p className={cn(typography.bodySmall, 'text-muted-foreground')}>
                                AI will identify emerging topics as you add more knowledge
                              </p>
                            </Card>
                          </Grid.Col>
                        </Grid>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </main>
      </ResponsiveStack>
    </LoadingOverlay>
  );
}